/** 鼠标位置 */
export interface MenuPosition {
  x: number;
  y: number;
}

/** 操作项 */
type ActionKey = string;

/** 操作组 */
type MenuGroupItem = [string, ContextMenuType];

/** 菜单项 = 操作项 | 操作组 */
type MenuItem = ActionKey | MenuGroupItem;

/** 菜单数据 */
export type ContextMenuType = MenuItem[][];

/** 菜单匹配项 */
export interface ContextMenuOptions {
  x: number;
  y: number;
  menus: ContextMenuType;
  binding?: any; // 绑定数据
}

/** 菜单实例 */
export interface ContextMenuInstance {
  /**
   * 打开菜单
   * @param options 菜单选项
   * @param options.x 鼠标x坐标
   * @param options.y 鼠标y坐标
   * @param options.menus 菜单数据
   * @param options.binding 绑定数据
   *  */
  open: (options: ContextMenuOptions) => void;
  close: () => void;
}
