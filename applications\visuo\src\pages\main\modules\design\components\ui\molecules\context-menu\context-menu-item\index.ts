import { defineComponent, computed, ref } from 'vue';
import type { PropType } from 'vue';
import type { ContextMenuType } from '@vis/document-core';
import { GraphBasic } from '@vis/document-core';
import { useActionStore } from '../../../../../stores';
import { useAction } from '../../../../../hooks';

export default defineComponent({
  name: 'vis-context-menu-item',
  components: {},
  props: {
    menus: {
      type: Array as PropType<ContextMenuType>,
      required: true
    },
    binding: {
      type: Object as PropType<GraphBasic>,
      default: null
    }
  },
  emits: ['action'],
  setup(props, { emit }) {
    const { actions, groups } = useActionStore();
    const actionSet = useAction();

    const getAction = (actionKey: string) => {
      return actions.value[actionKey];
    };

    const getGroup = (groupKey: string) => {
      return groups.value[groupKey];
    };

    const onHandler = (actionKey: string) => {
      // 调用操作方法
      // console.log('onHandler：', actionKey, props.binding);
      // (actionSet as any)[actionKey] && (actionSet as any)[actionKey]();
      emit('action', actionKey, props.binding);
    };

    const onActionEmit = (name: string, binding?: GraphBasic) => {
      emit('action', name, binding);
    };

    // #region 滚动条高度和最大高度计算
    const SAFE_MARGIN = 10; // 安全边距
    const MAX_HEIGHT = 700; // 最大高度

    /**
     * 计算当前菜单滚动区域高度
     * @param menus
     * @param rowHeight 每行高度
     * @param separatorHeight 分隔线高度
     * @param initialHeight 初始高度 padding * 2 + margin
     * @returns number
     * */
    const getScrollHeight = (
      menus: ContextMenuType,
      initialHeight = 20,
      rowHeight = 28,
      separatorHeight = 5
    ): number => {
      return menus.reduce((pre, group, index) => {
        index + 1 < menus.length && (pre += separatorHeight);
        return pre + group.length * rowHeight;
      }, initialHeight);
    };

    // 动态最大高度计算
    const scrollMaxHeightComp = computed(() => {
      const innerSpace = window.innerHeight - SAFE_MARGIN * 2;
      return Math.min(innerSpace, MAX_HEIGHT);
    });

    // #endregion

    const getKeyCode = (keyboard: number | string | number[]) => {
      return actionSet.getKeyCode(keyboard);
    };

    return {
      getAction,
      getGroup,
      onHandler,
      scrollMaxHeightComp,
      getScrollHeight,
      onActionEmit,
      getKeyCode
    };
  }
});
