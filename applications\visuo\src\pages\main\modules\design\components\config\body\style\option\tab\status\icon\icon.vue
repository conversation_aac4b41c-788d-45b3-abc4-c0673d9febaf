<template>
  <div class="vis-config-status-icon">
    <div class="vis-form-inline q-pt-sm">
      <vis-tab-icon-selector :icon-option="iconStyle" />

      <template v-if="iconStyle.type === 'picture'">
        <div class="vis-form-field">
          <div class="vis-form-field__label">宽度</div>
          <div class="vis-form-field__content">
            <vis-number v-model="iconStyle.width" icon="hticon-vis-letter-w" :min="0" />
          </div>
        </div>

        <div class="vis-form-field">
          <div class="vis-form-field__label">高度</div>
          <div class="vis-form-field__content">
            <vis-number v-model="iconStyle.height" icon="hticon-vis-letter-h" :min="0" />
          </div>
        </div>
      </template>
      <div class="vis-form-field">
        <div class="vis-form-field__label">位置</div>
        <div class="vis-form-field__content">
          <vis-button-group v-model="iconStyle.position" :options="iconPositionOptions" />
        </div>
      </div>

      <div class="vis-form-field">
        <div class="vis-form-field__label">与文字间距</div>
        <div class="vis-form-field__content">
          <vis-number v-model="iconStyle.gutter" icon="hticon-vis-max-width" :min="0" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./icon.ts"></script>
