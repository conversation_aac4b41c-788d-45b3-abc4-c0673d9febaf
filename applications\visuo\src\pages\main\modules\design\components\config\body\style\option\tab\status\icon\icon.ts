import { iconPositionOptions, type IconOption } from '@vis/document-core';
import { computed, defineComponent, ref, type PropType } from 'vue';
import VisTabIconSelector from '../../icon/icon.vue';

/**
 * 图标状态组件
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-config-status-icon',
  components: {
    VisTabIconSelector
  },
  props: {
    /**
     * 图标配置
     */
    statusOption: {
      type: Object as PropType<IconOption>,
      required: true
    }
  },
  setup(props) {
    const iconOption = ref(props.statusOption);
    const iconStyle = computed(() => props.statusOption);

    const iconOptions = [
      { label: '图片', value: 'picture' },
      { label: '图标', value: 'icon' }
    ];

    const iconPickerRef = ref();

    const fixIconName = computed(() => iconStyle.value.icon && iconStyle.value.icon.name);

    const showIconPicker = (e: Event) => {
      e.stopPropagation();
      iconPickerRef.value?.showPopup(e);
    };

    const handleUpdateIcon = (icon: IconOption) => {
      iconStyle.value.image = icon.image ? JSON.parse(JSON.stringify(icon.image)) : icon.image;
    };
    return {
      iconStyle,
      iconOption,
      iconPositionOptions,
      iconOptions,
      fixIconName,
      iconPickerRef,
      showIconPicker,
      handleUpdateIcon
    };
  }
});
