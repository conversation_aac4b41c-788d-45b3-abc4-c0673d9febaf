<template>
  <draggable
    :list="nodes"
    :group="{ name: 'vis-page-item', pull: true }"
    ghost-class="vis-store-page-ghost"
    drag-class="vis-store-page-drag"
    class="vis-store-page"
    item-key="id"
    :data-item-key="itemKey"
    :disabled="isDisabledDrag || isSearchHide"
    :animation="0"
    :empty-insert-threshold="0"
    :move="onCheckMove"
    @add="onDragAdd"
    @start="onDragStart"
    @end="onDragEnd"
  >
    <template #item="{ element }">
      <div
        :class="[
          'vis-store-page-node',
          element.children && element.children.length ? 'vis-store-page-node__parent' : 'vis-store-page-node__child',
          isSelected(element) && 'selected',
          isDragTargetParent(element) && 'drag-target-parent'
        ]"
      >
        <div
          class="vis-store-page-header"
          @click="onSelect(element, $event)"
          @contextmenu.stop.prevent="onOpenContextMenu(element, $event)"
        >
          <q-icon
            v-if="element.children && element.children.length"
            name="keyboard_arrow_right"
            class="vis-store-page-header-arrow"
            :class="{ rotate: isExpanded(element) }"
            @click.stop="toggleExpand(element, $event)"
          />
          <div class="vis-store-page-header-content" @dblclick="onDoubleClick(element, $event)">
            <!-- 标题 -->
            <div class="vis-store-page-header-content-title">
              <div class="vis-store-page-header-content-title-icon" :class="{ 'w-4 h-4': !element.children.length }">
                <!-- <ht-icon v-if="isGraphIcon(element)" :name="getGraphIcon(element, 'icon')" class="vis-icon" />
                <q-icon v-else :name="'o_layers'" />
                <q-tooltip v-if="isGraphIcon(element)" anchor="top middle" self="center middle">
                  {{ getGraphIcon(element, 'name') }}
                </q-tooltip> -->

                <!-- <q-icon v-if="element.id === homePageId" name="o_home" /> -->
                <q-icon v-if="isActivated(element)" name="o_check" />
                <!-- <q-icon v-else name="o_layers"></q-icon> -->
              </div>
              <!-- 双击编辑 -->
              <q-input
                v-if="isEditing(element)"
                v-model="editingName"
                dense
                borderless
                class="vis-store-page-header-content-title-input"
                @blur="onSaveName(element)"
                @keyup.enter="onSaveName(element)"
                @keyup.esc="onCancelEdit"
                ref="editInputRef"
              />
              <div v-else class="vis-store-page-header-content-title-text truncate">{{ element.name }}</div>
            </div>
          </div>
        </div>
        <div
          v-if="element.children && element.children.length"
          class="vis-store-page-collapsible"
          :class="{ folder: !isExpanded(element) }"
        >
          <!-- 递归渲染子节点 -->
          <div class="vis-store-page-children">
            <vis-page-item
              :nodes="element.children"
              :data-item-key="element.id"
              :selected-ids="selectedIds"
              @update:selected-ids="onEmitSelected"
              :expanded-map="expandedMap"
              @update:expanded-map="onEmitExpanded"
              :active-page-id="activePageId"
              :home-page-id="homePageId"
              :drag-target-parent-id="dragTargetParentId"
              @drag-add="onDragAdd"
              @drag-end="onDragEnd"
              @drag-start="onDragStart"
              @drag-move="onCheckMove"
              @context-menu="onOpenContextMenu"
            />
          </div>
        </div>
      </div>
    </template>
  </draggable>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="scss" src="./index.scss"></style>
