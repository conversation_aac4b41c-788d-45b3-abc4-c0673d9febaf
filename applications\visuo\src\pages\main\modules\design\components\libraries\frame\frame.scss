@import '../../index.scss';
.#{$vis-prefix}-frame {
  .#{$vis-prefix}-frame-title {
    position: absolute;
    top: -20px;
    left: 0;
    color: #909399;
    font-size: 12px;
    cursor: pointer;
    white-space: nowrap;

    input {
      padding: 0;
      border: none;
      outline: none;
      background: transparent;
      color: #909399;
    }
  }

  &.active {
    > .#{$vis-prefix}-frame-title {
      z-index: 99999;
      color: $primary;
    }
  }
  &.chosen-frame {
    > .wrap {
      outline: 1px dashed $canvas-theme-color;
    }
  }

  &.active,
  &.chosen-frame,
  &.active-frame {
    > .#{$vis-prefix}-frame-grid-ghost {
      @apply absolute w-full h-full pointer-events-none;
      z-index: $active-ghost-z-index;

      div {
        border: 1px solid rgba($canvas-theme-color, 0.4);

        &.active {
          background: rgba($canvas-theme-color, 0.2);
        }
      }
    }
  }

  &.grid > .wrap {
    > .#{$vis-prefix}-graph.dragging {
      opacity: 0.1 !important;
    }
  }
}
