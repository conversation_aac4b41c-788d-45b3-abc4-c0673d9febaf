<template>
  <q-menu v-model="visible" touch-position context-menu :target="false" @before-hide="onBeforeHide">
    <div ref="menuRef" class="vis-context-menu vis-menu" :style="menuStyle" @click.stop @contextmenu.prevent.stop>
      <div class="vis-context-menu-content">
        <vis-context-menu-item :menus="contextMenu" :binding="binding" @action="onActionEmit" />
      </div>
    </div>
  </q-menu>
</template>
<script lang="ts" src="./index.ts"></script>
<style lang="scss" src="./index.scss"></style>
