import { useMaterialDialog } from '@hetu/platform-app';
import { AttachmentService, isUUID } from '@hetu/platform-shared';
import { type IconOption, Image } from '@vis/document-core';
import { computed, defineComponent, ref, watch, type PropType } from 'vue';

/**
 * 图标选择器组件
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-tab-icon-selector',
  props: {
    iconOption: {
      type: Object as PropType<IconOption>,
      required: true
    }
  },
  setup(props) {
    const iconPickerRef = ref();

    const image = ref(props.iconOption.image || new Image());

    const imageUrl = ref('');

    const inputUrl = ref('');

    const showMenuType = ref(false);

    const iconOpt = computed(() => props.iconOption);

    const fixIconName = computed(
      () => props.iconOption.type === 'icon' && props.iconOption.icon && props.iconOption.icon.name
    );

    const fixPicUrl = computed(() => props.iconOption.type === 'picture' && props.iconOption.image?.url);

    /**
     * 图片变化时提交更新
     */
    watch(
      () => image.value.url,
      () => {
        const newurl = image.value.url;
        if (newurl && !isUUID(newurl)) {
          imageUrl.value = newurl;
          inputUrl.value = newurl;
        } else {
          imageUrl.value = AttachmentService.downloadFileUrl(newurl);
          inputUrl.value = newurl;
        }
        if (iconOpt.value) {
          iconOpt.value.image = image.value;
        }
      },
      {
        immediate: true
      }
    );

    const showPopup = (e: Event) => {
      e.stopPropagation();
      if (props.iconOption.type === 'icon') {
        iconPickerRef.value?.showPopup(e);
      } else {
        // 图片选择器处理
        selectImage();
      }
    };

    const selectImage = async () => {
      const { HtMaterialDialog } = await useMaterialDialog();

      HtMaterialDialog(image.value.url, 'image').onOk((value) => {
        image.value.url = value;
      });
    };

    const onSetUrl = () => {
      image.value.url = inputUrl.value;
    };

    const showIconPicker = (e: Event) => {
      e.stopPropagation();
      iconPickerRef.value?.showPopup(e);
    };

    const handleType = (value: 'picture' | 'icon') => {
      iconOpt.value.type = value;
      showMenuType.value = false;
    };

    return {
      iconPickerRef,
      fixIconName,
      fixPicUrl,
      imageUrl,
      inputUrl,
      showMenuType,
      iconOpt,
      showPopup,
      onSetUrl,
      showIconPicker,
      handleType
    };
  }
});
