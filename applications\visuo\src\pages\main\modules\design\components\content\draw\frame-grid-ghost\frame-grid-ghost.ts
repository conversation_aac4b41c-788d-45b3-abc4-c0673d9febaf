import { useDesignStore } from '../../../../stores';
import { defineComponent, computed, watch, ref, nextTick } from 'vue';
import { Frame, GraphType, useLayout } from '@vis/document-core';
import { addEvent, removeEvent } from '@hetu/platform-shared';
import { useGraph } from '../../../../hooks';
import { throttle } from 'quasar';
import type { Records } from '@hetu/util';

/**
 * 网格布局行高列宽设置
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-frame-grid-ghost',
  components: {},
  props: {},
  setup(props) {
    const designStore = useDesignStore();
    const activeGraph = computed(() => designStore.active.value.graph as Frame);
    const frameState = computed(() => designStore.frameState.value);
    const canvasState = computed(() => designStore.canvasState.value);
    const moveableState = computed(() => designStore.moveableState.value);

    const { isGrid } = useLayout();
    const { getCanvasPosSize, getGraphXYByCanvas } = useGraph();

    // 行样式
    const rowsStyle = ref({});
    // 列样式
    const colsStyle = ref({});
    // 网格大小
    const gridSize = ref([0, 0]);
    // 网格尺寸值
    const rowsSize = ref<
      Array<{
        state: '' | 'edit' | 'active';
        value: string;
      }>
    >([]);
    const columnsSize = ref<
      Array<{
        state: '' | 'edit' | 'active';
        value: string;
      }>
    >([]);
    // 鼠标放置的行列
    const hoverRowCol = ref([0, 0]);

    // 左侧行的外层样式
    const ghostLeftStyle = ref<Records>({});
    // 上侧列的外层样式
    const ghostTopStyle = ref<Records>({});

    // 是否显示
    const isGhost = computed(() => {
      return activeGraph.value && activeGraph.value.type === GraphType.Frame && isGrid(activeGraph.value as Frame);
    });
    // 是否在画布中拖拽或改变尺寸
    const isMoveable = computed(() => {
      return (
        activeGraph.value &&
        !canvasState.value.dragging.includes(activeGraph.value.id) &&
        !canvasState.value.resizeing.includes(activeGraph.value.id)
      );
    });

    // moveable的左上点的位置
    const pos1 = computed(() => moveableState.value?.pos1);
    const transform = ref('');

    // 最外层的样式
    const style = computed(() => {
      const s: Records = {
        position: 'absolute',
        transform: transform.value
      };
      if (isSizeActive.value) {
        s.width = `${activeGraph.value.width}px`;
        s.height = `${activeGraph.value.height}px`;
      }
      return s;
    });

    /**
     * 当activeGraph的位置、宽高、旋转角度变化时ghost的位置也要变化
     */
    watch(
      () => pos1.value,
      () => {
        const moveableDom = document.querySelector('.vis-design-canvas--moveable') as HTMLElement;
        if (isGhost.value && activeGraph.value && moveableDom && pos1.value) {
          transform.value = moveableDom.style.transform;

          const { width, height } = activeGraph.value;
          const moveableRect = designStore.moveableRef.value?.getRect();
          ghostLeftStyle.value = {
            height: height + 'px',
            transform: `translate(${pos1.value[0]}px, ${pos1.value[1]}px) rotate(${moveableRect?.rotation}deg) translate(-50px)`,
            transformOrigin: '0px 0px'
          };

          ghostTopStyle.value = {
            width: width + 'px',
            transform: `translate(${pos1.value[0]}px, ${pos1.value[1]}px) rotate(${moveableRect?.rotation}deg) translate(0, -50px)`,
            transformOrigin: '0px 0px'
          };
        }
      },
      { deep: true }
    );

    // 当前正在拖拽改变列宽或行高
    const resizeing = ref(false);
    // 改变的下标
    const sizeIndex = ref(0);
    // 改变的是row或col
    const sizeType = ref('');

    /**
     * 选中图形的自动布局配置变化时，需要更新ghost的row、column样式
     */
    watch(
      [() => activeGraph.value?.id, () => activeGraph.value?.autoLayout],
      () => {
        if (isGhost.value && activeGraph.value) {
          const frame = activeGraph.value as Frame;
          rowsSize.value = [];
          columnsSize.value = [];
          const { horizontalGap, verticalGap, padding, gridRowsSizing, gridColumnsSizing } = frame.autoLayout;

          gridSize.value = frame.autoLayout.gridSize;
          const templateRows = gridRowsSizing.map((size, i) => {
            rowsSize.value.push({
              state: resizeing.value && sizeType.value === 'row' && sizeIndex.value === i ? 'active' : '',
              value: `${size}`
            });
            return isNaN(parseInt(`${size}`)) ? 'minmax(0, 1fr)' : `${size}px`;
          });
          const templateColumns = gridColumnsSizing.map((size, i) => {
            columnsSize.value.push({
              state: resizeing.value && sizeType.value === 'col' && sizeIndex.value === i ? 'active' : '',
              value: `${size}`
            });
            return isNaN(parseInt(`${size}`)) ? 'minmax(0, 1fr)' : `${size}px`;
          });
          rowsStyle.value = {
            display: 'grid',
            gridTemplateRows: templateRows.join(' '),
            padding: `${padding[0]}px 0 ${padding[2]}px`,
            gap: `${verticalGap}px`
          };
          colsStyle.value = {
            display: 'grid',
            gridTemplateColumns: templateColumns.join(' '),
            padding: `0 ${padding[1]}px 0 ${padding[3]}px`,
            gap: `${horizontalGap}px`
          };
        }
      },
      {
        deep: true
      }
    );

    /**
     * 鼠标移动时计算放置在哪个格子里
     */
    const onMouseMoveWrap = throttle((e: MouseEvent) => {
      if (activeGraph.value && isMoveable.value) {
        const frame = activeGraph.value;

        // 基于整个画布的鼠标位置
        const rect = getCanvasPosSize(e.clientX, e.clientY);

        if (frameState.value[frame.id]) {
          // 计算frame在画布里的位置
          let targetFrameX = frame.transform.translate[0];
          let targetFrameY = frame.transform.translate[1];
          if (frame.parent) {
            const position = getGraphXYByCanvas(frame);
            targetFrameX = position.x;
            targetFrameY = position.y;
          }

          // 计算鼠标在frame中的位置
          const offsetX = rect.x - targetFrameX;
          const offsetY = rect.y - targetFrameY;

          const gridItemRect = frameState.value[frame.id].gridItemRect;
          if (gridItemRect) {
            const keys = Object.keys(gridItemRect);
            let col = 0;
            let row = 0;
            // 按鼠标位置计算
            for (let i = 0; i < keys.length; i++) {
              const grid = keys[i];
              const { x, y, w, h } = gridItemRect[grid];
              if (!row && offsetY <= y + h) {
                row = parseInt(grid.split('-')[0]);
              }
              if (!col && offsetX <= x + w) {
                col = parseInt(grid.split('-')[1]);
              }
              if (row && col) break;
            }
            hoverRowCol.value = [row, col];
          }
        }
      }
    }, 100);

    const onMouseLeaveWrap = (e: MouseEvent) => {
      hoverRowCol.value = [0, 0];
    };

    let wrap: HTMLElement;

    watch(
      () => activeGraph.value?.id,
      () => {
        if (activeGraph.value) {
          wrap = document.querySelector(`[id="${activeGraph.value.id}"] > .wrap`) as HTMLElement;
          if (isGhost.value) {
            addEvent(wrap, 'mousemove', onMouseMoveWrap);
            addEvent(wrap, 'mouseleave', onMouseLeaveWrap);
            return;
          }
        }

        if (wrap) {
          removeEvent(wrap, 'mousemove', onMouseMoveWrap);
          removeEvent(wrap, 'mouseleave', onMouseLeaveWrap);
        }
      }
    );

    //#region 编辑名称
    const inputRef = ref<HTMLInputElement[]>();

    const isSizeActive = computed(
      () => rowsSize.value.find((rs) => rs.state === 'active') || columnsSize.value.find((cs) => cs.state === 'active')
    );
    const onClickTip = (i: number, type: 'row' | 'col') => {
      rowsSize.value.forEach((rs, index) => {
        if (type === 'row') {
          i !== index && (rs.state = '');
        } else {
          rs.state = '';
        }
      });
      columnsSize.value.forEach((cs, index) => {
        if (type === 'col') {
          i !== index && (cs.state = '');
        } else {
          cs.state = '';
        }
      });
      if (type === 'row') {
        if (rowsSize.value[i].state === '') {
          rowsSize.value[i].state = 'active';
        } else {
          rowsSize.value[i].state = 'edit';
        }
      } else {
        if (columnsSize.value[i].state === '') {
          columnsSize.value[i].state = 'active';
        } else {
          columnsSize.value[i].state = 'edit';
        }
      }

      nextTick(() => {
        if (inputRef.value?.length) {
          inputRef.value[0].focus();
          inputRef.value[0].select();
        }
      });
    };

    const onBlurSize = (i: number, type: 'row' | 'col') => {
      if (activeGraph.value) {
        if (type === 'row') {
          rowsSize.value[i].state = '';
          let value: number | string = rowsSize.value[i].value;
          value = isNaN(parseInt(value)) ? 'Auto' : parseInt(value);
          activeGraph.value.autoLayout.gridRowsSizing[i] = value as number | 'Auto';
          rowsSize.value[i].value = `${value}`;
        } else {
          columnsSize.value[i].state = '';
          let value: number | string = columnsSize.value[i].value;
          value = isNaN(parseInt(value)) ? 'Auto' : parseInt(value);
          activeGraph.value.autoLayout.gridColumnsSizing[i] = value as number | 'Auto';
          columnsSize.value[i].value = `${value}`;
        }
      }
    };
    //#endregion

    //#region 拖拽改变列宽行高
    const height = ref(0);
    const width = ref(0);
    const startY = ref(0);
    const startX = ref(0);

    const onMouseDown = (e: MouseEvent, index: number, type: 'row' | 'col') => {
      resizeing.value = true;
      startY.value = e.clientY;
      startX.value = e.clientX;
      const parent = (e.target as HTMLElement).closest('.rect') as HTMLElement;
      if (parent) {
        height.value = parent.offsetHeight || 0;
        width.value = parent.offsetWidth || 0;
        sizeIndex.value = index;
        sizeType.value = type;
      }

      document.body.style.cursor = `${type}-resize`;
      document.body.style.userSelect = 'none';

      addEvent(document, 'mousemove', onMouseMove);
      addEvent(document, 'mouseup', onMouseUp);
    };
    const onMouseMove = (e: MouseEvent) => {
      if (resizeing.value) {
        if (sizeType.value === 'row') {
          const deltaY = e.clientY - startY.value;
          let h = height.value + deltaY;
          h = Math.max(h, 1);
          activeGraph.value.autoLayout.gridRowsSizing[sizeIndex.value] = h;
        } else {
          const deltaX = e.clientX - startX.value;
          let w = width.value + deltaX;
          w = Math.max(w, 1);
          activeGraph.value.autoLayout.gridColumnsSizing[sizeIndex.value] = w;
        }
      }
    };

    const onMouseUp = (e: MouseEvent) => {
      resizeing.value = false;
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
      removeEvent(document, 'mousemove', onMouseMove);
      removeEvent(document, 'mouseup', onMouseUp);
    };

    const onGhostClick = (e: Event) => {
      if (!(e.target as HTMLElement).closest('.ghost')) {
        console.log('clear');
        rowsSize.value.forEach((rs, index) => {
          rs.state = '';
        });
        columnsSize.value.forEach((cs, index) => {
          cs.state = '';
        });
      }
    };
    //#endregion

    return {
      activeGraph,
      isGhost,
      isMoveable,

      style,
      ghostLeftStyle,
      ghostTopStyle,
      rowsStyle,
      colsStyle,
      gridSize,
      rowsSize,
      columnsSize,
      hoverRowCol,

      isSizeActive,
      inputRef,
      onClickTip,
      onBlurSize,

      onMouseDown,
      onMouseMove,
      onMouseUp,
      onGhostClick
    };
  }
});
