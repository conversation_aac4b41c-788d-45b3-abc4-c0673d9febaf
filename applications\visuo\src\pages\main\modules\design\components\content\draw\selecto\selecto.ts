import { defineComponent, ref, computed, nextTick } from 'vue';
import { useDesignStore, useActionStore } from '../../../../stores';
import { VueSelecto } from 'vue3-selecto';
import type Selecto from 'selecto';
import type { ElementType, OnDragEnd, OnDragStart, OnScroll, OnSelect, OnSelectEnd } from 'selecto';
import type Moveable from 'vue3-moveable';
import { deepFlat } from '../helper/types';
import { useAction, useGraph } from '../../../../hooks';
import { Frame, useLayout } from '@vis/document-core';

export default defineComponent({
  name: 'vis-design-selecto',
  components: {
    VueSelecto
  },
  props: {},
  setup(props) {
    const designStore = useDesignStore();
    const actionStore = useActionStore();
    const { move } = useAction();

    const { addFrame, addTextBox, activeGraphs, findGraph } = useGraph();
    const { isFreeform } = useLayout();

    const horizontalGuidesRef = designStore.horizontalGuidesRef;
    const verticalGuidesRef = designStore.verticalGuidesRef;
    const infiniteCanvasRef = designStore.infiniteCanvasRef;
    const moveableRef = designStore.moveableRef;

    const selectoRef = ref<Selecto>();

    // moveable的拖拽目标
    const moveableTargets = designStore.moveableTargets;

    // dock栏
    const actions = actionStore.actions;
    const handAction = actions.value.hand;

    //#region selectTo

    // 是否从目标内部选择（如果 hitTest 为 0，也会变为内部选择）
    const isSelectFromInside = computed(() => {
      // 默认情况下不允许从frame内部拖拽画框选择组件，当添加容器时允许从其他容器上拖拽画框添加组件
      if (actions.value.frame.active || actions.value.textbox.active) {
        return true;
      }
      // 按住command键或ctrl时允许从frame内部拖拽画框选择组件
      if (actionStore.isMetaCtrl.value) {
        return true;
      }
      return false;
    });

    const className = computed(() => {
      if (actions.value.frame.active) {
        return 'add-frame';
      }

      if (actions.value.textbox.active) {
        return 'add-textbox';
      }
      return 'selected';
    });

    // 滚动条配置
    const selectoScrollOptions = computed(() => {
      return {
        container: () => {
          return infiniteCanvasRef.value?.getContainer();
        },
        threshold: 0,
        throttleTime: 30,
        getScrollPosition: () => {
          const current = infiniteCanvasRef.value;
          return [current?.getScrollLeft(), current?.getScrollTop()];
        }
      };
    });

    /**
     * 是否允许开始拖拽画框
     * @param e
     * @returns true: 允许拖拽，false: 不允许拖拽
     */
    const dragCondition = (e: OnDragStart) => {
      // 组件未选中，未按住空格键拖拽画布
      const flag = !handAction.active;

      return flag;
    };

    /**
     * 开始拖拽事件
     */
    const onSelectoDragStart = (e: OnDragStart) => {
      const moveable = moveableRef.value as Moveable;

      if (moveable) {
        const target = e.inputEvent.target;
        const flatted = deepFlat(moveableTargets.value);
        const { isClick } = e;
        // 添加容器
        const isAdd = actions.value.frame.active || actions.value.textbox.active;
        // 当前拖拽元素是否在图形内
        const isContains = flatted.some((t: any) => t === target || t.contains(target));

        // 框选后改变位置时不能在拖拽
        if (
          designStore.active.value.graphIds.length > 1 &&
          // 检查目标是否是可移动中包含的元素
          moveable.isMoveableElement(target)
        ) {
          e.stop();
        }

        // vis-frame-grid-layout-ghost时，不触发框选
        if (target.closest('.ghost') || target.closest('.vis-frame-grid-layout-ghost')) {
          e.stop();
        }

        // if (moveable.isMoveableElement(target)) {
        //   e.stop();
        // }
        // if (!isAdd) {
        //   e.stop();
        // }
        // if (
        //   // 检查目标是否是可移动中包含的元素
        //   // moveable.isMoveableElement(target) ||
        //   // 1. 移动到容器内时
        //   // 2. 在容器内拖拽画框添加容器
        //   // isContains &&
        //   // 添加容器时要框选不能阻止
        //   !isAdd
        // ) {
        //   // 阻止拖拽
        //   e.stop();
        //   // 1.添加容器时不能拖拽
        // }
      }
    };

    /**
     * 结束拖拽事件
     * @param e
     */
    const onSelectoDragEnd = (e: OnDragEnd) => {
      // console.log('onSelectoDragEnd', e);
    };

    /**
     * 框选开始事件
     * @param e
     */
    const onSelectToStart = (e: OnSelect) => {
      //  console.log('onSelectToStart');
    };

    /**
     * 框选结束事件
     * @param e
     */
    const onSelectToEnd = (e: OnSelectEnd) => {
      const { isDragStartEnd, isClick, added, removed, afterAdded, afterRemoved, selected, inputEvent } = e;

      //console.log('ToEnd', e, selected);
      // console.log('ToEnd:', 'isTrusted:', e.isTrusted, 'isClick:', e.isClick, 'isDragStartEnd:', e.isDragStartEnd);
      const moveable = moveableRef.value as Moveable;
      // isDragStartEnd: 是否在拖拽开始后立即结束
      // if (isDragStartEnd) {
      //   inputEvent.preventDefault();
      //   moveable.waitToChangeTarget().then(() => {
      //     moveable.dragStart(inputEvent);
      //   });
      // }

      // 添加图形
      if (actions.value.frame.active || actions.value.textbox.active) {
        if (!e.isClick && e.rect.width > 10 && e.rect.height > 10) {
          // 添加容器
          if (actions.value.frame.active) {
            addFrame(e.rect.left, e.rect.top, e.rect.width, e.rect.height);
          }
          if (actions.value.textbox.active) {
            addTextBox(e.rect.left, e.rect.top, e.rect.width, e.rect.height);
          }
          // 拖拽选框添加容器后改变鼠标的状态
          move();
        }
      } else {
        const graphEle = selected.filter((ele) => {
          return !ele.classList.contains('lock') && !ele.classList.contains('hidden');
        });

        // 框选
        let selectEle: ElementType[] = graphEle;

        if (selected.length > 1) {
          // 过滤掉内层图形
          const targetFrame = e.inputEvent.target.closest('.vis-frame');

          if (targetFrame) {
            const frame = findGraph(targetFrame.id) as Frame;
            // 在容器里选择,只有自由布局才能框选
            if (frame && isFreeform(frame)) {
              const frameId = targetFrame.id;
              selectEle = graphEle?.filter((ele) => ele.getAttribute('parent') === frameId) || [];
            } else {
              selectEle = targetFrame;
            }
          } else {
            // 根级选择
            selectEle = graphEle?.filter((ele) => ele.getAttribute('parent') === '') || [];
          }
        }
        nextTick(() => {
          activeGraphs(selectEle as HTMLElement[]);
        });
      }
    };

    const onSelectoScroll = ({ direction }: OnScroll) => {
      infiniteCanvasRef.value?.scrollBy(direction[0] * 10, direction[1] * 10);
    };

    //#endregion

    return {
      // 是否允许从目标内部拖动画框
      isSelectFromInside,
      className,

      selectoRef,
      selectoScrollOptions,
      dragCondition,
      onSelectoDragStart,
      onSelectToStart,
      onSelectoDragEnd,
      onSelectToEnd,
      onSelectoScroll,

      moveableRef,
      moveableTargets
    };
  },
  mounted() {
    const designStore = useDesignStore();
    designStore.selectoRef.value = this.selectoRef;
  },
  methods: {}
});
