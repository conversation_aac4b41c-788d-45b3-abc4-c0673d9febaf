import { computed, defineComponent, nextTick, onBeforeMount, onMounted, ref, watch } from 'vue';
import { DatasetField, DatasetService, type DatasetTreeNode } from '@hetu/metadata-shared';
import {
  Block,
  Graph,
  useDocumentStore,
  WidgetBlock,
  WidgetType,
  type WidgetConfigDataMatch,
  AxisField,
  useWidgetConfigStore
} from '@vis/document-core';
import { useDesignStore } from '../../../../../stores';
import { useQuasar } from 'quasar';
import VisDatasetTree from './dataset/dataset.vue';
import VisFieldList from './field-list/field-list.vue';
import VisFieldItem from './field-item/field-item.vue';
import VisDataSheet from './data-sheet/data-sheet.vue';
import { useDataset } from '../../../../../hooks';

/**
 * 数据配置
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-config-data',
  components: {
    VisDataSheet,
    VisDatasetTree,
    VisFieldList,
    VisFieldItem
  },
  setup() {
    const { active, rightWidth, aggregatorFunctions } = useDesignStore();

    const activeGraph = computed(() => active.value.graph as Graph);

    const docStore = useDocumentStore();

    // 获取当前选中的组件
    const activeBlock = computed(() => {
      const blockId = (activeGraph.value as Block)?.decoration;
      if (!blockId) return null;
      return (docStore.document.value.blocks.find((b) => b.id === blockId) as WidgetBlock) || null;
    });

    const dataMapping = computed(() => activeBlock.value?.dataMapping || {});

    // 选择数据资源
    const selectDataset = (data: DatasetTreeNode) => {
      if (!activeBlock.value) return;
      activeBlock.value.datasetId = data.id;
      activeBlock.value.datasetType = data.datasetType || '';
      loadFieldList();
    };

    const datasetName = ref('');

    const fieldMapping = ref<{ datasetName: string; datasetField: DatasetField[] }>();

    const { getDataTypeIcon, getStaticDataField } = useDataset();

    // 获取字段列表
    const loadFieldList = async () => {
      if (!activeBlock.value?.datasetId) return;
      if (activeBlock.value.datasetType === 'static') {
        const datasetField = (await getStaticDataField(activeBlock.value.datasetId)).sort(
          (a, b) => a.fieldType?.localeCompare(b.fieldType || '') || 0
        );
        const res = {
          datasetField: datasetField,
          datasetName: activeBlock.value.datasetId === 'STATIC_DATA' ? '内置数据' : activeBlock.value.datasetId
        };
        datasetName.value = res.datasetName;

        fieldMapping.value = res;
        return;
      }
      DatasetService.getFieldListById(activeBlock.value.datasetId).then((res) => {
        if (res) {
          res.datasetField = res.datasetField?.sort((a, b) => a.fieldType?.localeCompare(b.fieldType || '') || 0);
          fieldMapping.value = res;
          datasetName.value = res.datasetName;
        }
      });
    };

    const { dataConfigs } = useWidgetConfigStore();

    const widgetDataConfig = computed(() => {
      if (!activeBlock.value?.type) return {};
      const config = dataConfigs.get(activeBlock.value.type as WidgetType);
      return (config?.matchs as Record<string, WidgetConfigDataMatch>) || {};
    });

    /**
     * 加载聚合函数
     */
    const loadAggFun = () => {
      if (!activeBlock.value?.datasetId) return;
      if (activeBlock.value.datasetType === 'static') {
        aggregatorFunctions.value = [];
        return;
      }
      DatasetService.getFunctions(activeBlock.value.datasetId).then((data) => {
        aggregatorFunctions.value = data;
      });
    };

    const getFieldIcon = (fieldDatatype: string) => {
      return 'field-' + getDataTypeIcon(fieldDatatype);
    };

    // 删除过滤字段
    const removeFilterField = (index: number) => {
      if (dataMapping.value['filters'] && dataMapping.value['filters'].length > index) {
        dataMapping.value['filters'].splice(index, 1);
      }
    };

    const refreshTypeOptions = [
      {
        label: '不刷新',
        value: 'none'
      },
      {
        label: '受控模式',
        value: 'control'
      },
      {
        label: '自动刷新',
        value: 'auto'
      }
    ];

    const filterActiveIds = computed(
      () => dataMapping.value['filters']?.map((item: AxisField) => item.id || item.fieldName) || []
    );

    const addFilterField = (fields: DatasetField[]) => {
      !dataMapping.value['filters'] && (dataMapping.value['filters'] = []);

      // 获取可用字段的ID列表
      const availableFieldIds = fields.map((field) => field.id);

      // 移除不存在的过滤器字段
      dataMapping.value['filters'] = dataMapping.value['filters'].filter((filter: AxisField) =>
        availableFieldIds.includes(filter.id || '')
      );

      // 添加新的字段
      fields.forEach((field: DatasetField) => {
        const existingFilterIndex = dataMapping.value['filters'].findIndex(
          (filter: AxisField) => filter.id === field.id
        );

        if (existingFilterIndex === -1) {
          dataMapping.value['filters'].push({
            ...field
          });
        }
      });
    };

    // 打开过滤弹窗
    const openFilterDialog = (index: number) => {
      // console.log(dataMapping.value['filters'][index]);
    };

    const $q = useQuasar();

    const openDataSheetDialog = () => {
      const staticData = docStore.staticDatas.value;

      // 打开data-sheet
      $q.dialog({
        component: VisDataSheet,
        componentProps: {
          modelValue: staticData,
          datasetId: activeBlock.value?.datasetId,
          onClose: async (data: any) => {
            docStore.document.value.staticData = data;
          }
        }
      });
    };

    watch(
      () => activeBlock.value?.datasetId,
      () => {
        if (activeBlock.value?.datasetId) {
          loadFieldList();
          loadAggFun();
          activeBlock.value!.dataMapping = {};
        }
      }
    );

    onBeforeMount(async () => {
      if (activeBlock.value?.datasetId) {
        await loadFieldList();
        loadAggFun();
      }
    });

    return {
      rightWidth,
      activeBlock,
      selectDataset,
      dataMapping,
      fieldMapping,
      datasetName,
      widgetDataConfig,
      removeFilterField,
      getFieldIcon,
      refreshTypeOptions,
      openFilterDialog,
      openDataSheetDialog,
      filterActiveIds,
      addFilterField
    };
  }
});
