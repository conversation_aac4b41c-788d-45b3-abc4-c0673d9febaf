<template>
  <draggable
    :list="nodes"
    :group="{ name: 'vis-layer-item', pull: true }"
    ghost-class="vis-store-layer-ghost"
    drag-class="vis-store-layer-drag"
    class="vis-store-layer"
    item-key="id"
    :data-item-key="itemKey"
    :disabled="isDisabledDrag"
    :animation="0"
    :empty-insert-threshold="0"
    @add="onDragAdd"
    @start="onDragStart"
    @end="onDragEnd"
    @move="onCheckMove"
  >
    <template #item="{ element }">
      <div
        :class="[
          'vis-store-layer-node',
          element.children && element.children.length ? 'vis-store-layer-node__parent' : 'vis-store-layer-node__child',
          isSelected(element) && 'selected',
          !element.visible && 'opacity-30',
          isDragTargetParent(element) && 'drag-target-parent'
        ]"
      >
        <div
          class="vis-store-layer-header"
          :class="{
            hovered: isHovered(element),
            active: isActive(element)
          }"
          @click="onSelect(element)"
          @mouseenter="onHoverEvent($event, element)"
          @mouseleave="onHoverEvent($event, element)"
          @contextmenu.stop.prevent="onOpenContextMenu(element, $event)"
        >
          <q-icon
            v-if="element.children && element.children.length"
            name="keyboard_arrow_right"
            class="vis-store-layer-header-arrow"
            :class="{ rotate: isExpanded(element) }"
            @click.stop="toggleExpand(element, $event)"
          />
          <div class="vis-store-layer-header-content" @dblclick="onDoubleClick(element, $event)">
            <!-- 标题 -->
            <div class="vis-store-layer-header-content-title">
              <div class="vis-store-layer-header-content-title-icon">
                <ht-icon v-if="isGraphIcon(element)" :name="getGraphIcon(element, 'icon')" class="vis-icon" />
                <q-icon v-else :name="'o_layers'" />
                <q-tooltip v-if="isGraphIcon(element)" anchor="top middle" self="center middle">
                  {{ getGraphIcon(element, 'name') }}
                </q-tooltip>
              </div>
              <!-- 双击编辑 -->
              <q-input
                v-if="isEditing(element)"
                v-model="editingName"
                dense
                borderless
                class="vis-store-layer-header-content-title-input"
                @blur="onSaveName(element)"
                @keyup.enter="onSaveName(element)"
                @keyup.esc="onCancelEdit"
                ref="editInputRef"
              />
              <div v-else class="vis-store-layer-header-content-title-text truncate">{{ element.name }}</div>
            </div>
            <!-- 操作栏 -->
            <div
              v-if="!isEditing(element)"
              class="vis-store-layer-header-content-actions"
              :class="{ show: isHovered(element) || isActive(element) }"
            >
              <q-btn
                :unelevated="true"
                :ripple="false"
                class="tool_icon"
                :class="{ 'tool_icon-active': !element.visible }"
                @click.stop="onVisibleChange(element)"
                @dblclick.stop
              >
                <ht-icon :name="element.visible ? 'hticon-vis-eye-o' : 'hticon-vis-eye-c'" />
              </q-btn>
              <q-btn
                :unelevated="true"
                :ripple="false"
                class="tool_icon"
                :class="{ 'tool_icon-active': element.locked }"
                @click.stop="onLockedChange(element)"
                @dblclick.stop
              >
                <q-icon :name="element.locked ? 'o_lock' : 'o_lock_open'" size="12px" />
              </q-btn>
            </div>
          </div>
        </div>
        <!--  && element.children.length > 0  -->
        <div v-if="element.children" class="vis-store-layer-collapsible" :class="{ folder: !isExpanded(element) }">
          <!-- 递归渲染子节点 -->
          <div class="vis-store-layer-children">
            <vis-layer-item
              :nodes="element.children"
              :data-item-key="element.id"
              @drag-add="onDragAdd"
              @drag-start="onDragStart"
              @drag-end="onDragEnd"
              @drag-move="onCheckMove"
              :drag-target-parent-id="dragTargetParentId"
              :selected-ids="selectedIds"
              @update:selected-ids="onEmitSelected"
              :expanded-map="expandedMap"
              @update:expanded-map="onEmitExpanded"
              @context-menu="onOpenContextMenu"
            />
          </div>
        </div>
      </div>
    </template>
  </draggable>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="scss" src="./index.scss"></style>
