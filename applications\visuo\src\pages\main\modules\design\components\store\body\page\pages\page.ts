import { GraphType, Page, Group, Graph, GraphBasic, useDocumentStore } from '@vis/document-core';
import type { ClickOutsideElement, ContextMenuInstance, ContextMenuType } from '@vis/document-core';
import { contentBaseHash } from '@hetu/platform-shared';
import { defineComponent, ref, computed, watch, nextTick, onMounted } from 'vue';
import type { Directive, DirectiveBinding } from 'vue';
import { debounce } from 'quasar';
import draggable from 'vuedraggable';
import VisPageItem from './page-item/index.vue';
import { usePage  } from '../../../../../hooks';
import { useDesignStore } from '../../../../../stores';
import { difference } from 'lodash-es';

/**
 * 页面
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-store-page',
  components: { draggable, VisPageItem },
  props: {
    pagesHeight: {
      type: Number,
      required: true
    }
  },
  directives: {
    'click-outside': {
      mounted(el: ClickOutsideElement, binding: DirectiveBinding) {
        if (typeof binding.value !== 'function') return;

        const handler = (event: MouseEvent) => {
          if (el.contains(event.target as Node)) return;
          binding.value(event);
        };

        el._clickOutsideHandler = handler;

        document.addEventListener('click', handler);
      },
      unmounted(el: ClickOutsideElement) {
        if (el._clickOutsideHandler) {
          document.removeEventListener('click', el._clickOutsideHandler);
          delete el._clickOutsideHandler;
        }
      }
    }
  },
  setup() {
    // 文档状态
    const docStore = useDocumentStore();
    const documentComp = computed(() => docStore.document.value);
    const pageComp = computed(() => documentComp.value.children.map((page) => ({ ...page, children: [] })));

    // 设计器状态
    const designStore = useDesignStore();
    const active = computed(() => designStore.active.value);
    const activePage = computed(() => active.value.page);

    const { findPage, addPage, switchPage, addPageToGroup, movePageToGroup, getPageIds, sortPages } = usePage();

    const pages = ref<(Page | Group)[]>([]);

    // #region 搜索
    const keyWord = ref<string>('');
    const filterRef = ref<HTMLInputElement>();
    const onKeyWordChange = debounce((value: string | number | null): void => {
      const filterPages = !value ? pageComp.value : filterTree(pageComp.value, value as string);
      pages.value = pageToTree(filterPages);
    }, 300);

    // 根据关键字筛选页面
    const filterTree = (nodes: Page[], keyword: string): Page[] => {
      if (!keyword) return nodes;
      return nodes.filter((page) => [page.name, page.group].some((attr) => attr?.includes(keyword)));
    };

    // 重置
    const resetFilter = () => {
      keyWord.value = '';
      filterRef.value?.focus();
    };
    // 搜索显示/隐藏
    const isSearchHide = ref<boolean>(false);
    const onUpdateSearchHide = () => {
      if (keyWord.value) return;
      if (!isSearchHide.value) {
        setTimeout(() => filterRef.value?.focus(), 0);
      } else {
        keyWord.value = '';
      }
      isSearchHide.value = !isSearchHide.value;
    };
    // #endregion

    // #region 添加页面/组
    const isDisabledGroup = computed(() => {
      const page = findPage(activePageId.value);
      return page && !!page.group;
    });
    const onAdd = (type: 'page' | 'group') => {
      if (type === 'page') {
        const page = addPage();
        setTimeout(() => {
          activePageId.value = page.id; // 当前页面
          selectedIds.value = [page.id]; // 选中效果
        }, 0);
      } else if (type === 'group') {
        addPageToGroup(activePageId.value);
      }
    };
    // #endregion

    // #region 展开收起
    const collectExpandedIds = (groups: (Group | Page)[]) => {
      const ids: string[] = [];
      const traverse = (nodes: (Group | Page)[]) => {
        nodes.forEach((node: Group | Page) => {
          if (node.children && node.children.length > 0) {
            ids.push(node.id);
            traverse((node as Group).children);
          }
        });
      };
      traverse(groups);
      return ids;
    };

    // 初始化收集展开状态
    const initExpandedState = (nodes: (Group | Page)[]) => {
      if (!expandedMap.value.size) {
        collectExpandedIds(nodes).forEach((id) => expandedMap.value.set(id, true));
        return;
      } else {
        const newIds = collectExpandedIds(nodes);
        const oldIds = Array.from(expandedMap.value.keys());
        const addIds = difference(newIds, oldIds);
        const delIds = difference(oldIds, newIds);

        delIds.forEach((id) => expandedMap.value.delete(id));
        addIds.forEach((id) => expandedMap.value.set(id, true));
      }
    };

    const expandedMap = ref(new Map<string, boolean>());
    const isExpandHide = ref<boolean>(true);
    const onUpdateExpandAllHide = () => {
      isExpandHide.value = !isExpandHide.value;
      if (isExpandHide.value) {
        // collectExpandedIds(pages.value).forEach((id) => expandedMap.value.set(id, true));
        expandedMap.value.forEach((value, key) => expandedMap.value.set(key, true));
      } else {
        expandedMap.value.forEach((value, key) => expandedMap.value.set(key, false));

        const page = findPage(activePageId.value) as Page; // 当前选中页
        const group = pages.value.find((group) => group.type === GraphType.Group && group.name === page.group); // 当前选中分组
        if (group) {
          expandedMap.value.set(group.id, true);
        }
      }
    };
    const onUpdateExpandHide = (node: Group) => {
      expandedMap.value.set(node.id, !expandedMap.value.get(node.id));
    };
    // #endregion

    // #region 拖拽
    const isDragging = ref<boolean>(false);
    const dragTargetParentId = ref<string | null>(null);
    const onDragStart = (evt: any) => {
      isDragging.value = true;
      dragTargetParentId.value = null;
    };

    const onDragEnd = (evt: any) => {
      // console.log(evt, 'Page-onDragEnd');
      isDragging.value = false;
      dragTargetParentId.value = null;
      sortPages(getPageIds(pages.value));
    };

    const onDragAdd = (evt: any) => {
      // console.log(evt, 'Page-onDragAdd');
      const element = evt.item['__draggable_context'].element; // 当前拖拽元素
      movePageToGroup(element.id, evt.to.dataset.itemKey, pages.value);
    };

    const onCheckMove = (evt: any) => {
      // console.log('onCheckMove:', evt);
      // 检测是否为嵌套拖拽（跨容器拖拽）
      dragTargetParentId.value = null;

      if (evt.from && evt.to && evt.from !== evt.to) {
        // 获取目标容器的 data-item-key 属性，即父节点的ID
        const targetParentId = evt.to.dataset?.itemKey;
        if (targetParentId) {
          // 设置目标父节点ID
          dragTargetParentId.value = targetParentId;
        }
      }
    };
    // #endregion

    // #region 选中高亮 - 支持多选
    // 主页面
    const homePageId = computed(() => documentComp.value.home);
    // 当前页面
    const activePageId = ref<string>('');
    // 选中页面
    const selectedIds = ref<string[]>([]);
    const onUpdateSelected = (node: Page | Group, $event: PointerEvent) => {
      if ($event.ctrlKey) {
        if (!selectedIds.value.includes(node.id)) {
          selectedIds.value.push(node.id);
        } else {
          selectedIds.value = selectedIds.value.filter((i) => i !== node.id);
        }
      } else {
        if (node.type === GraphType.Page && node.id !== activePageId.value) {
          switchPage(node.id); // 切换页面
          activePageId.value = node.id; // 当前页面
        }
        selectedIds.value = [node.id]; // 选中效果
      }
    };

    const onClickOutside = () => {
      selectedIds.value = [];
    };
    // #endregion

    // #region 右键菜单
    const contextMenuRef = ref<ContextMenuInstance>();
    // 配置菜单
    const pageMenus: ContextMenuType = [
      ['addToPageGroup', 'deletePage', 'copyPage', 'renamePage'],
      ['setHomePage', 'addIcon']
    ];
    const pageGroupMenus: ContextMenuType = [['cancelPageGroup', 'deletePageGroup', 'renamePageGroup'], ['addIcon']];
    const onOpenContextMenu = (node: Graph, event: MouseEvent) => {
      nextTick(() => {
        contextMenuRef.value?.open({
          menus: node.type === GraphType.Page ? pageMenus : pageGroupMenus,
          x: event.clientX,
          y: event.clientY,
          binding: node
        });
      });
    };
    const onCloseContextMenu = () => {
      // console.log('onCloseContextMenu => ');
    };
    const onActionClick = (name: string, binding?: GraphBasic) => {
      console.log('page:onActionClick => ', name, binding);
    };
    // #endregion

    // #region 构造页面数据
    const pageToTree = (pages: Page[]) => {
      // 变量存储
      const results: (Group | Page)[] = [];
      const groupMap = new Map<string, Group>();

      // 初始化分组
      const initGroup = (page: Page): Group => {
        const group = new Group();
        group.id = contentBaseHash(page.group, 30);
        group.name = page.group;
        group.order = page.order;
        return group;
      };

      // 构建分组
      pages.forEach((page: Page) => {
        if (page.group) {
          groupMap.set(page.group, groupMap.get(page.group) || initGroup(page));
          const group = groupMap.get(page.group) as Group;

          group.children.push(page);
        } else {
          results.push(page);
        }
      });

      // 根据order排序
      results.sort((a, b) => b.order - a.order);
      groupMap.forEach((group) => {
        // 组内页面排序
        group.children.sort((a, b) => b.order - a.order);
        // 计算group的order - 页面order最大值
        group.order = group.children.reduce((acc, cur) => Math.max(acc, cur.order), 0);
      });

      // 合并返回 - 排序结果
      return [...groupMap.values(), ...results].sort((a, b) => b.order - a.order);
    };
    // #endregion

    // #region 监听页面列表和页面对象属性改动 - children不监听
    watch(
      pageComp,
      (page, o_page) => {
        // 构造
        pages.value = pageToTree(page);
        // 收集展开状态 - note: group id 每次构建都会重新生成
        initExpandedState(pages.value);
      },
      {
        immediate: true
      }
    );
    // #endregion

    onMounted(() => {
      nextTick(() => {
        activePageId.value = activePage.value.id || documentComp.value.home;
      });
    });

    return {
      pages,
      expandedMap,
      isExpandHide,
      onUpdateExpandAllHide,
      onUpdateExpandHide,
      isSearchHide,
      onUpdateSearchHide,
      keyWord,
      onKeyWordChange,
      resetFilter,
      filterRef,
      isDisabledGroup,
      onAdd,
      onDragEnd,
      onDragAdd,
      onDragStart,
      onCheckMove,
      dragTargetParentId,
      isDragging,
      homePageId,
      activePageId,
      selectedIds,
      onUpdateSelected,
      onClickOutside,

      onOpenContextMenu,
      onCloseContextMenu,
      onActionClick,
      contextMenuRef
    };
  }
});
