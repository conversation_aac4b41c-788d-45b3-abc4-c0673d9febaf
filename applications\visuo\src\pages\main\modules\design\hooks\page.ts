import { computed, ref } from 'vue';
import { useDesignStore } from '../stores';
import { Group, GraphType, Page, useDocumentStore } from '@vis/document-core';
import { UUID } from '@hetu/platform-shared';
import { isString, cloneDeep } from 'lodash-es';
import { useQuasar } from 'quasar';

/**
 * 页面操作
 * <AUTHOR>
 */
export const usePage = () => {
  const $q = useQuasar();
  const designStore = useDesignStore();
  const documentStore = useDocumentStore();

  const documentComp = computed(() => documentStore.document.value);

  const pagesComp = computed(() => documentComp.value.children);

  const groupCounter = ref<number>(0);
  const groupCounterComp = computed(() => ++groupCounter.value);

  /**
   * 根据id查找页面
   * @param id
   * @returns
   */
  const findPage = (id: string): Page | undefined => {
    return pagesComp.value.find((page) => page.id === id) as Page;
  };

  /**
   * 切换页面
   * @param page
   * @returns
   * */
  const switchPage = (page: Page | string) => {
    designStore.reset();
    if (isString(page)) {
      const target = findPage(page);
      if (target) {
        return (designStore.active.value.page = target);
      }
    }
    designStore.active.value.page = page as Page;
  };

  /**
   * 新增空白页面
   * @param page
   * @returns pageInstance
   * */
  const addPage = () => {
    // 新增实例
    const pageInstance = new Page();
    // 设置属性
    pageInstance.name = `页面 ${pagesComp.value.length + 1}`;
    pageInstance.order = pagesComp.value.length;
    // 加入列表
    pagesComp.value.push(pageInstance);
    // 切换页面
    designStore.active.value.page = pageInstance;

    // 返回实例
    return pageInstance;
  };

  /**
   * 添加到页面组
   * @param page
   * @returns
   * */
  const addPageToGroup = (page: Page | string) => {
    const targetPage = !isString(page) ? page : findPage(page);
    if (targetPage) {
      targetPage.group = `分类 ${groupCounterComp.value}`;
    }
  };

  /**
   * 移动到页面组
   * @param page
   * @returns
   * */
  const movePageToGroup = (page: Page | string, groupId: string, groups: (Group | Page)[]) => {
    const targetPage = !isString(page) ? page : findPage(page);
    if (targetPage) {
      const group = groups.find((group) => group.id === groupId) as Group;
      const filters = pagesComp.value.filter((page) => page.group === targetPage.group) as Page[];
      targetPage.group = group ? group.name : '';
      // console.log(group,'group', 'filters', filters);
      // 排序
      filters.length <= 1 && sortPages(getPageIds(groups));
    }
  };

  /**
   * 获取页面的ids数组
   * @param groups
   * @returns
   * */
  const getPageIds = (groups: (Group | Page)[]) => {
    const ids: string[] = [];
    groups.forEach((group) => {
      if (group.children && group.children.length > 0) {
        ids.push(...getPageIds(group.children as Page[]));
      } else {
        ids.push(group.id);
      }
    });
    return ids;
  };

  /**
   * 根据ids排序页面
   * @param pageIds
   * @returns
   * */
  const sortPages = (pageIds: string[]) => {
    // 创建id-index映射
    const idIndexMap = new Map();
    pageIds.forEach((pageId, index) => idIndexMap.set(pageId, index));
    // 创建原数组副本排序
    const results = [...pagesComp.value].sort((a, b) => {
      const indexA = idIndexMap.has(a.id) ? idIndexMap.get(a.id) : Infinity;
      const indexB = idIndexMap.has(b.id) ? idIndexMap.get(b.id) : Infinity;

      // 如果某个ID不在指定的IDs数组中，将其排在最后
      return indexA - indexB;
    });
    // 更新order
    results.forEach((page, index, _) => (page.order = _.length - 1 - index)); // 更新order

    // 更新原数组
    documentComp.value.children = results;
  };

  /**
   * 修改分组/页面名称
   * @param pageIds
   * @param name
   * */
  const renamePage = (pageIds: string[], name: string, type: GraphType) => {
    pagesComp.value.forEach((page) => {
      if (pageIds.includes(page.id)) {
        if (type === GraphType.Page) {
          page.name = name;
        } else if (type === GraphType.Group) {
          page.group = name;
        }
      }
    });
  };

  /**
   * 删除页面
   * @param page
   * @returns
   * */
  const deletePage = (page: Page | string) => {
    if (pagesComp.value.length <= 1) {
      $q.notify({
        type: 'negative',
        message: '无法删除所有页面，需至少保留一个页面',
        position: 'top'
      });
      return;
    }

    const targetPage = !isString(page) ? page : findPage(page);
    if (targetPage) {
      const index = pagesComp.value.findIndex((page) => page.id === targetPage.id);
      pagesComp.value.splice(index, 1);
    }
  };

  /**
   * 复制页面
   * @param page
   * @returns
   * */
  const clonePage = (page: Page | string) => {
    const targetPage = !isString(page) ? page : findPage(page);
    if (targetPage) {
      // 克隆
      const clonedPage = cloneDeep(targetPage);
      clonedPage.id = UUID();
      clonedPage.name = `${clonedPage.name} 副本`;
      // 当前位置相邻处
      const index = pagesComp.value.findIndex((page) => page.id === targetPage.id);
      pagesComp.value.splice(index, 0, clonedPage);
    }
  };

  /**
   * 设置为主页
   * @param page
   * @returns
   * */
  const setHomePage = (page: Page | string) => {
    const targetPage = !isString(page) ? page : findPage(page);
    if (targetPage) {
      documentStore.document.value.home = targetPage.id;
    }
  };

  return {
    findPage,
    switchPage,
    addPage,
    addPageToGroup,
    movePageToGroup,
    getPageIds,
    sortPages,
    renamePage,
    deletePage,
    clonePage,
    setHomePage
  };
};
