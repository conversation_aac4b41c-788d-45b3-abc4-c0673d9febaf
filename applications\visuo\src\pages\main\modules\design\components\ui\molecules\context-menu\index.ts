import { defineComponent, ref, computed, nextTick, watch } from 'vue';
import type { MenuPosition, ContextMenuType, ContextMenuOptions } from '@vis/document-core';
import { GraphBasic } from '@vis/document-core';
import VisContextMenuItem from './context-menu-item/index.vue';

/**
 * 右键菜单
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-context-menu',
  components: { VisContextMenuItem },
  props: {},
  emits: ['open', 'close', 'action'],
  setup(props, { emit, expose }) {
    const visible = ref<boolean>(false);
    const position = ref<MenuPosition>({ x: 0, y: 0 });
    const contextMenu = ref<any>([]);
    const binding = ref<any>(null);

    // #region 计算菜单位置
    const menuRef = ref<HTMLElement>();
    const SAFE_MARGIN = 10; // 安全边距

    const adjustedPosition = ref<MenuPosition>({ x: 0, y: 0 });
    const isPositionAdjusted = ref(false);

    const menuStyle = computed(() => {
      const positionTemp = isPositionAdjusted.value ? adjustedPosition.value : position.value;
      return {
        position: 'fixed' as const,
        left: `${positionTemp.x}px`,
        top: `${positionTemp.y}px`,
        zIndex: 6000
      };
    });

    const adjustMenuPosition = () => {
      if (!visible.value || !menuRef.value) return;
      // DOM加载完调整位置
      nextTick(() => {
        const menu = menuRef.value;

        // 获取菜单容器和视口的尺寸
        const rect = menu!.getBoundingClientRect();
        const menuWidth = rect.width;
        const menuHeight = rect.height;
        const vw = window.innerWidth;
        const vh = window.innerHeight;

        // 使用当前的位置作为基准
        let { x, y } = isPositionAdjusted.value ? adjustedPosition.value : position.value;

        // 水平位置计算
        const rightSpace = vw - x - SAFE_MARGIN; // 鼠标到右侧距离
        if (rightSpace < menuWidth) {
          x = vw - menuWidth - SAFE_MARGIN;
        }

        // 垂直位置计算 - 高度动态变化
        const bottomSpace = vh - y - SAFE_MARGIN; // 鼠标到底部距离
        if (bottomSpace < menuHeight) {
          y = vh - menuHeight - SAFE_MARGIN;
        }

        // 确保不超出左边界
        if (x < SAFE_MARGIN) {
          x = SAFE_MARGIN;
        }

        // 确保不超出上边界
        if (y < SAFE_MARGIN) {
          y = SAFE_MARGIN;
        }

        // 更新响应式位置数据
        adjustedPosition.value = { x, y };
        isPositionAdjusted.value = true;
      });
    };
    // #endregion

    // #region 实例方法
    const open = (options: ContextMenuOptions) => {
      // console.log('open => ', options);
      contextMenu.value = options.menus;
      position.value = {
        x: options.x,
        y: options.y
      };

      // 绑定节点数据
      options.binding && (binding.value = options.binding);

      visible.value = true;

      emit('open');
    };

    const close = () => {
      visible.value = false;
    };
    // #endregion

    // 菜单隐藏前
    const onBeforeHide = () => {
      contextMenu.value = [];
      position.value = { x: 0, y: 0 };
      binding.value = null;
      visible.value = false;

      emit('close');
    };

    // action-emit
    const onActionEmit = (name: string, binding?: GraphBasic) => {
      emit('action', name, binding);
    };

    // 监听 visible 和 position 属性变化，支持连续右键触发
    watch(
      (): [Boolean, MenuPosition] => [visible.value, position.value],
      ([visible, position], [o_visible, o_position]) => {
        // 只有在菜单可见或位置确实变化时重新调整
        if (visible || (o_position && (position.x !== o_position.x || position.y !== o_position.y))) {
          nextTick(() => {
            adjustMenuPosition();
          });
        }
        isPositionAdjusted.value = false;
      },
      { deep: true }
    );

    expose({
      open,
      close
    });

    return {
      visible,
      position,
      contextMenu,
      menuRef,
      menuStyle,
      binding,
      open,
      close,
      onBeforeHide,
      onActionEmit
    };
  }
});
