<template>
  <q-scroll-area :style="{ maxHeight: `${scrollMaxHeightComp}px`, height: `${getScrollHeight(menus)}px` }">
    <q-list dense>
      <template v-for="(group, groupIndex) in menus" :key="groupIndex">
        <template v-for="(action, index) in group" :key="index">
          <!-- 菜单项 -->
          <template v-if="typeof action === 'string'">
            <q-item
              clickable
              v-close-popup
              :disable="getAction(action).disable"
              @click.prevent="onHandler(getAction(action).name)"
            >
              <!-- <q-item-section avatar v-if="getAction(action).icon">
                <q-icon :name="getAction(action).icon" size="8" />
              </q-item-section> -->
              <q-item-section>
                {{ getAction(action).title }}
              </q-item-section>
              <q-item-section side>
                <label>
                  <span v-for="(keyboard, index) in getAction(action).shortcuts" :key="index">
                    <span v-if="index > 0">&nbsp;+</span>
                    {{ getKeyCode(keyboard) }}
                  </span>
                </label>
              </q-item-section>
            </q-item>
          </template>

          <!-- 菜单组 -->
          <template v-else-if="Array.isArray(action)">
            <q-item>
              <q-item-section>{{ getGroup(action[0]).title }}</q-item-section>
              <q-item-section side>
                <q-icon name="keyboard_arrow_right" />
              </q-item-section>

              <!-- 嵌套菜单 -->
              <q-menu anchor="top right" self="top left" :offset="[20, 0]" class="vis-menu overflow-hidden">
                <div class="vis-context-menu-content">
                  <vis-context-menu-item :menus="action[1]" :binding="binding" @action="onActionEmit" />
                </div>
              </q-menu>
            </q-item>
          </template>
        </template>

        <!-- 分组分隔线 -->
        <q-separator v-if="groupIndex + 1 < menus.length" :key="`sep-${groupIndex}`" />
      </template>
    </q-list>
  </q-scroll-area>
</template>
<script lang="ts" src="./index.ts"></script>
